import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';
import { GoogleGenerativeAIEmbeddings } from '@langchain/google-genai';
import { writeFile, unlink, readFile } from 'fs/promises';
import { join } from 'path';
import { tmpdir } from 'os';
import pdf from 'pdf-parse';

// Initialize embeddings with <PERSON> (fallback to better models available)
const embeddings = new GoogleGenerativeAIEmbeddings({
  apiKey: process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY!,
  modelName: "embedding-001", // TODO: Switch to better embedding model
});

// Text splitter for chunking documents
const textSplitter = new RecursiveCharacterTextSplitter({
  chunkSize: 1000,
  chunkOverlap: 200,
});

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const configId = formData.get('configId') as string;

    if (!file || !configId) {
      return NextResponse.json({ 
        error: 'File and configId are required' 
      }, { status: 400 });
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'text/plain',
      'text/markdown'
    ];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        error: 'Unsupported file type. Please upload PDF, TXT, or MD files.'
      }, { status: 400 });
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({ 
        error: 'File size too large. Maximum size is 10MB.' 
      }, { status: 400 });
    }

    console.log(`[Document Upload] Processing file: ${file.name}, type: ${file.type}, size: ${file.size}`);

    // Create temporary file
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const tempFilePath = join(tmpdir(), `upload_${Date.now()}_${file.name}`);
    await writeFile(tempFilePath, buffer);

    let extractedContent = '';

    try {
      // Load document based on type
      switch (file.type) {
        case 'application/pdf':
          // Suppress pdf-parse warnings by temporarily capturing console.warn
          const originalWarn = console.warn;
          console.warn = (message: any) => {
            // Only suppress the specific font warning, allow other warnings through
            if (typeof message === 'string' && message.includes('Ran out of space in font private use area')) {
              return;
            }
            originalWarn(message);
          };

          try {
            const pdfData = await pdf(buffer, {
              // Add options to improve parsing
              max: 0, // No limit on pages
              normalizeWhitespace: false, // Preserve original whitespace
              disableCombineTextItems: false // Allow text combining for better readability
            });
            extractedContent = pdfData.text;

            // Check if we got any text
            if (!extractedContent || extractedContent.trim().length === 0) {
              console.warn('[Document Upload] No text extracted from PDF, trying alternative approach...');

              // Try with different options as fallback
              const fallbackData = await pdf(buffer, {
                max: 0,
                normalizeWhitespace: true,
                disableCombineTextItems: true
              });

              extractedContent = fallbackData.text;

              if (!extractedContent || extractedContent.trim().length === 0) {
                throw new Error('No text could be extracted from this PDF. The file may be image-based, password-protected, or corrupted.');
              }
            }

            // Log some useful info about the PDF
            console.log(`[Document Upload] PDF processed: ${pdfData.numpages || 'unknown'} pages, ${extractedContent.length} characters`);
          } catch (pdfError: any) {
            console.error('[Document Upload] PDF parsing error:', pdfError);
            throw new Error(`Failed to process PDF: ${pdfError.message || 'Unknown error'}`);
          } finally {
            // Restore original console.warn
            console.warn = originalWarn;
          }
          break;

        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          // For DOCX, we'll need a different approach since we removed DocxLoader
          // For now, let's throw an error and handle this separately
          throw new Error('DOCX support temporarily disabled. Please use PDF, TXT, or MD files.');

        case 'text/plain':
        case 'text/markdown':
          extractedContent = await readFile(tempFilePath, 'utf-8');
          break;

        default:
          throw new Error('Unsupported file type');
      }

      console.log(`[Document Upload] Extracted ${extractedContent.length} characters from ${file.name}`);

      // Create a document object for text splitting
      const document = {
        pageContent: extractedContent,
        metadata: {
          source: file.name,
          type: file.type
        }
      };

      // Split document into chunks
      const chunks = await textSplitter.splitDocuments([document]);
      console.log(`[Document Upload] Split into ${chunks.length} chunks`);

      // Store document metadata in database
      const { data: documentRecord, error: docError } = await supabase
        .from('documents')
        .insert({
          user_id: user.id,
          custom_api_config_id: configId,
          filename: file.name,
          file_type: file.type,
          file_size: file.size,
          content: extractedContent,
          metadata: {
            chunks_count: chunks.length,
            processing_started_at: new Date().toISOString()
          },
          status: 'processing'
        })
        .select()
        .single();

      if (docError) {
        console.error('[Document Upload] Error storing document:', docError);
        throw new Error('Failed to store document metadata');
      }

      console.log(`[Document Upload] Stored document record: ${documentRecord.id}`);

      // Process chunks and generate embeddings
      const chunkPromises = chunks.map(async (chunk, index) => {
        try {
          // Generate embedding for this chunk
          const embedding = await embeddings.embedQuery(chunk.pageContent);
          
          // Store chunk with embedding
          const { error: chunkError } = await supabase
            .from('document_chunks')
            .insert({
              document_id: documentRecord.id,
              user_id: user.id,
              custom_api_config_id: configId,
              content: chunk.pageContent,
              metadata: {
                ...chunk.metadata,
                chunk_index: index,
                chunk_size: chunk.pageContent.length
              },
              embedding: embedding
            });

          if (chunkError) {
            console.error(`[Document Upload] Error storing chunk ${index}:`, chunkError);
            throw chunkError;
          }

          return { success: true, index };
        } catch (error) {
          console.error(`[Document Upload] Error processing chunk ${index}:`, error);
          return { success: false, index, error };
        }
      });

      // Wait for all chunks to be processed
      const chunkResults = await Promise.all(chunkPromises);
      const successfulChunks = chunkResults.filter(r => r.success).length;
      const failedChunks = chunkResults.filter(r => !r.success).length;

      console.log(`[Document Upload] Processed ${successfulChunks}/${chunks.length} chunks successfully`);

      // Update document status
      const finalStatus = failedChunks === 0 ? 'completed' : 'failed';
      await supabase
        .from('documents')
        .update({
          status: finalStatus,
          metadata: {
            ...documentRecord.metadata,
            chunks_processed: successfulChunks,
            chunks_failed: failedChunks,
            processing_completed_at: new Date().toISOString()
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', documentRecord.id);

      // Clean up temporary file
      await unlink(tempFilePath);

      // Invalidate training cache to ensure immediate effect
      try {
        const { trainingDataCache } = await import('@/lib/cache/trainingCache');
        trainingDataCache.invalidate(configId);
        console.log(`[Document Upload] Cache invalidated for config: ${configId}`);
      } catch (error) {
        console.warn('[Document Upload] Cache invalidation failed:', error);
      }

      return NextResponse.json({
        success: true,
        document: {
          id: documentRecord.id,
          filename: file.name,
          status: finalStatus,
          chunks_processed: successfulChunks,
          chunks_total: chunks.length
        }
      });

    } catch (processingError) {
      console.error('[Document Upload] Processing error:', processingError);
      
      // Clean up temporary file
      try {
        await unlink(tempFilePath);
      } catch (unlinkError) {
        console.warn('[Document Upload] Failed to clean up temp file:', unlinkError);
      }

      throw processingError;
    }

  } catch (error: any) {
    console.error('[Document Upload] Error:', error);
    return NextResponse.json({
      error: 'Failed to process document',
      details: error.message
    }, { status: 500 });
  }
}
