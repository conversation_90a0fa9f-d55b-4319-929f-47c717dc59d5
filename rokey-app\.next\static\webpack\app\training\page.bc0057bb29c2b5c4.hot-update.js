"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/DocumentUpload.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentUpload.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DocumentUpload(param) {\n    let { configId, onDocumentUploaded } = param;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Load documents for the current config with retry logic\n    const loadDocuments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[loadDocuments]\": async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, expectedDocumentId = arguments.length > 1 ? arguments[1] : void 0;\n            if (!configId) return;\n            if (retryCount === 0) {\n                setIsRefreshing(true);\n            }\n            try {\n                const response = await fetch(\"/api/documents/list?configId=\".concat(configId));\n                if (response.ok) {\n                    const data = await response.json();\n                    const newDocuments = data.documents || [];\n                    // If we're looking for a specific document and it's not found, retry\n                    if (expectedDocumentId && retryCount < 3) {\n                        const foundDocument = newDocuments.find({\n                            \"DocumentUpload.useCallback[loadDocuments].foundDocument\": (doc)=>doc.id === expectedDocumentId\n                        }[\"DocumentUpload.useCallback[loadDocuments].foundDocument\"]);\n                        if (!foundDocument) {\n                            console.log(\"[DocumentUpload] Document \".concat(expectedDocumentId, \" not found, retrying in \").concat((retryCount + 1) * 500, \"ms...\"));\n                            setTimeout({\n                                \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                                    loadDocuments(retryCount + 1, expectedDocumentId);\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"], (retryCount + 1) * 500); // 500ms, 1s, 1.5s delays\n                            return;\n                        }\n                    }\n                    // Merge with existing documents, avoiding duplicates\n                    setDocuments({\n                        \"DocumentUpload.useCallback[loadDocuments]\": (prev)=>{\n                            const existingIds = new Set(prev.map({\n                                \"DocumentUpload.useCallback[loadDocuments]\": (doc)=>doc.id\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"]));\n                            const uniqueNewDocs = newDocuments.filter({\n                                \"DocumentUpload.useCallback[loadDocuments].uniqueNewDocs\": (doc)=>!existingIds.has(doc.id)\n                            }[\"DocumentUpload.useCallback[loadDocuments].uniqueNewDocs\"]);\n                            const updatedExistingDocs = prev.map({\n                                \"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs\": (doc)=>{\n                                    const serverDoc = newDocuments.find({\n                                        \"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs.serverDoc\": (d)=>d.id === doc.id\n                                    }[\"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs.serverDoc\"]);\n                                    return serverDoc || doc; // Use server data if available, otherwise keep existing\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs\"]);\n                            // Combine updated existing docs with unique new docs, sorted by creation date\n                            const allDocs = [\n                                ...updatedExistingDocs,\n                                ...uniqueNewDocs\n                            ];\n                            return allDocs.sort({\n                                \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                }\n            } catch (err) {\n                console.error('Failed to load documents:', err);\n                // Retry on error if we haven't exceeded retry count\n                if (retryCount < 2) {\n                    setTimeout({\n                        \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                            loadDocuments(retryCount + 1, expectedDocumentId);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"], 1000);\n                }\n            }\n        }\n    }[\"DocumentUpload.useCallback[loadDocuments]\"], [\n        configId\n    ]);\n    // Load documents when configId changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"DocumentUpload.useEffect\": ()=>{\n            loadDocuments();\n        }\n    }[\"DocumentUpload.useEffect\"], [\n        loadDocuments\n    ]);\n    // Handle file upload\n    const handleFileUpload = async (files)=>{\n        if (!configId) {\n            setError('Please select an API configuration first');\n            return;\n        }\n        const file = files[0];\n        if (!file) return;\n        // Validate file type\n        const allowedTypes = [\n            'application/pdf',\n            'text/plain',\n            'text/markdown'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setError('Please upload PDF, TXT, or MD files only');\n            return;\n        }\n        // Validate file size (10MB max)\n        if (file.size > 10 * 1024 * 1024) {\n            setError('File size must be less than 10MB');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(null);\n        setUploadProgress(0);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('configId', configId);\n            // Simulate progress for better UX\n            const progressInterval = setInterval(()=>{\n                setUploadProgress((prev)=>Math.min(prev + 10, 90));\n            }, 200);\n            const response = await fetch('/api/documents/upload', {\n                method: 'POST',\n                body: formData\n            });\n            clearInterval(progressInterval);\n            setUploadProgress(100);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Upload failed');\n            }\n            const result = await response.json();\n            console.log(\"[DocumentUpload] Upload successful:\", result);\n            setSuccess(\"✨ \".concat(file.name, \" uploaded successfully! Processing \").concat(result.document.chunks_total, \" chunks.\"));\n            // Optimistically add the document to the list immediately\n            const optimisticDocument = {\n                id: result.document.id,\n                filename: result.document.filename,\n                file_type: file.type,\n                file_size: file.size,\n                status: result.document.status,\n                chunks_count: result.document.chunks_processed || 0,\n                created_at: new Date().toISOString()\n            };\n            console.log(\"[DocumentUpload] Adding optimistic document:\", optimisticDocument);\n            setDocuments((prev)=>[\n                    optimisticDocument,\n                    ...prev\n                ]);\n            // Small delay to allow database transaction to commit, then reload with retry logic\n            setTimeout(async ()=>{\n                console.log(\"[DocumentUpload] Starting document list refresh for document: \".concat(result.document.id));\n                await loadDocuments(0, result.document.id);\n            }, 200);\n            // Call callback if provided\n            onDocumentUploaded === null || onDocumentUploaded === void 0 ? void 0 : onDocumentUploaded();\n        } catch (err) {\n            const errorMessage = \"Upload failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        } finally{\n            setIsUploading(false);\n            setUploadProgress(0);\n            // Clear file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Auto-clear success message after 5 seconds\n            if (success) {\n                setTimeout(()=>setSuccess(null), 5000);\n            }\n        }\n    };\n    // Handle drag and drop\n    const handleDrag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrag]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.type === 'dragenter' || e.type === 'dragover') {\n                setDragActive(true);\n            } else if (e.type === 'dragleave') {\n                setDragActive(false);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrag]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragActive(false);\n            if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n                handleFileUpload(e.dataTransfer.files);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrop]\"], [\n        configId\n    ]);\n    // Handle file input change\n    const handleInputChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            handleFileUpload(e.target.files);\n        }\n    };\n    // Delete document\n    const handleDeleteDocument = async (documentId)=>{\n        if (!confirm('Are you sure you want to delete this document?')) return;\n        // Optimistically remove the document from the list\n        const originalDocuments = documents;\n        setDocuments((prev)=>prev.filter((doc)=>doc.id !== documentId));\n        try {\n            const response = await fetch(\"/api/documents/\".concat(documentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                // Restore the original list on error\n                setDocuments(originalDocuments);\n                throw new Error('Failed to delete document');\n            }\n            setSuccess('Document deleted successfully');\n            // Refresh the list to ensure consistency\n            await loadDocuments();\n            // Auto-clear success message after 3 seconds\n            setTimeout(()=>setSuccess(null), 3000);\n        } catch (err) {\n            // Restore the original list on error\n            setDocuments(originalDocuments);\n            const errorMessage = \"Delete failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        }\n    };\n    // Format file size\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // Get file icon\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes('pdf')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 269,\n            columnNumber: 42\n        }, this);\n        if (fileType.includes('word')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 270,\n            columnNumber: 43\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-5 h-5 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 271,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm font-medium\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-800 text-sm font-medium\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 287,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform \".concat(dragActive ? 'border-orange-400 bg-orange-50 scale-105 shadow-lg' : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md', \" \").concat(!configId ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                onDragEnter: handleDrag,\n                onDragLeave: handleDrag,\n                onDragOver: handleDrag,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return configId && ((_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click());\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        accept: \".pdf,.txt,.md\",\n                        onChange: handleInputChange,\n                        disabled: !configId || isUploading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, this),\n                    isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-12 h-12 text-orange-500 mx-auto animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Processing Document...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(uploadProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            uploadProgress,\n                                            \"% complete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: configId ? 'Upload Knowledge Documents' : 'Select a configuration first'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: \"Drag and drop files here, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: \"Supports PDF, TXT, MD files up to 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Uploaded Documents\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            getFileIcon(doc.file_type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: doc.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            formatFileSize(doc.file_size),\n                                                            \" • \",\n                                                            doc.chunks_count,\n                                                            \" chunks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    doc.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-orange-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(doc.status === 'completed' ? 'text-green-600' : doc.status === 'processing' ? 'text-orange-600' : 'text-red-600'),\n                                                        children: doc.status === 'completed' ? 'Ready' : doc.status === 'processing' ? 'Processing' : 'Failed'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteDocument(doc.id),\n                                                className: \"p-1 text-gray-400 hover:text-red-500 transition-colors\",\n                                                title: \"Delete document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, doc.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"Wg3Csz6qp0hbrL5TIpe/0qAcs6A=\");\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentUpload.tsx\n"));

/***/ })

});