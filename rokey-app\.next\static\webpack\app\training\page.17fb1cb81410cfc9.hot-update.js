"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/DocumentUpload.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentUpload.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DocumentUpload(param) {\n    let { configId, onDocumentUploaded } = param;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Load documents for the current config with retry logic\n    const loadDocuments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[loadDocuments]\": async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, expectedDocumentId = arguments.length > 1 ? arguments[1] : void 0;\n            if (!configId) return;\n            if (retryCount === 0) {\n                setIsRefreshing(true);\n            }\n            try {\n                const response = await fetch(\"/api/documents/list?configId=\".concat(configId));\n                if (response.ok) {\n                    const data = await response.json();\n                    const newDocuments = data.documents || [];\n                    // If we're looking for a specific document and it's not found, retry\n                    if (expectedDocumentId && retryCount < 3) {\n                        const foundDocument = newDocuments.find({\n                            \"DocumentUpload.useCallback[loadDocuments].foundDocument\": (doc)=>doc.id === expectedDocumentId\n                        }[\"DocumentUpload.useCallback[loadDocuments].foundDocument\"]);\n                        if (!foundDocument) {\n                            console.log(\"[DocumentUpload] Document \".concat(expectedDocumentId, \" not found, retrying in \").concat((retryCount + 1) * 500, \"ms...\"));\n                            setTimeout({\n                                \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                                    loadDocuments(retryCount + 1, expectedDocumentId);\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"], (retryCount + 1) * 500); // 500ms, 1s, 1.5s delays\n                            return;\n                        }\n                    }\n                    // Merge with existing documents, avoiding duplicates\n                    setDocuments({\n                        \"DocumentUpload.useCallback[loadDocuments]\": (prev)=>{\n                            const existingIds = new Set(prev.map({\n                                \"DocumentUpload.useCallback[loadDocuments]\": (doc)=>doc.id\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"]));\n                            const uniqueNewDocs = newDocuments.filter({\n                                \"DocumentUpload.useCallback[loadDocuments].uniqueNewDocs\": (doc)=>!existingIds.has(doc.id)\n                            }[\"DocumentUpload.useCallback[loadDocuments].uniqueNewDocs\"]);\n                            const updatedExistingDocs = prev.map({\n                                \"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs\": (doc)=>{\n                                    const serverDoc = newDocuments.find({\n                                        \"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs.serverDoc\": (d)=>d.id === doc.id\n                                    }[\"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs.serverDoc\"]);\n                                    return serverDoc || doc; // Use server data if available, otherwise keep existing\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs\"]);\n                            // Combine updated existing docs with unique new docs, sorted by creation date\n                            const allDocs = [\n                                ...updatedExistingDocs,\n                                ...uniqueNewDocs\n                            ];\n                            return allDocs.sort({\n                                \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                }\n            } catch (err) {\n                console.error('Failed to load documents:', err);\n                // Retry on error if we haven't exceeded retry count\n                if (retryCount < 2) {\n                    setTimeout({\n                        \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                            loadDocuments(retryCount + 1, expectedDocumentId);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"], 1000);\n                }\n            } finally{\n                if (retryCount === 0 || expectedDocumentId) {\n                    setIsRefreshing(false);\n                }\n            }\n        }\n    }[\"DocumentUpload.useCallback[loadDocuments]\"], [\n        configId\n    ]);\n    // Load documents when configId changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"DocumentUpload.useEffect\": ()=>{\n            loadDocuments();\n        }\n    }[\"DocumentUpload.useEffect\"], [\n        loadDocuments\n    ]);\n    // Handle file upload\n    const handleFileUpload = async (files)=>{\n        if (!configId) {\n            setError('Please select an API configuration first');\n            return;\n        }\n        const file = files[0];\n        if (!file) return;\n        // Validate file type\n        const allowedTypes = [\n            'application/pdf',\n            'text/plain',\n            'text/markdown'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setError('Please upload PDF, TXT, or MD files only');\n            return;\n        }\n        // Validate file size (10MB max)\n        if (file.size > 10 * 1024 * 1024) {\n            setError('File size must be less than 10MB');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(null);\n        setUploadProgress(0);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('configId', configId);\n            // Simulate progress for better UX\n            const progressInterval = setInterval(()=>{\n                setUploadProgress((prev)=>Math.min(prev + 10, 90));\n            }, 200);\n            const response = await fetch('/api/documents/upload', {\n                method: 'POST',\n                body: formData\n            });\n            clearInterval(progressInterval);\n            setUploadProgress(100);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Upload failed');\n            }\n            const result = await response.json();\n            console.log(\"[DocumentUpload] Upload successful:\", result);\n            setSuccess(\"✨ \".concat(file.name, \" uploaded successfully! Processing \").concat(result.document.chunks_total, \" chunks.\"));\n            // Optimistically add the document to the list immediately\n            const optimisticDocument = {\n                id: result.document.id,\n                filename: result.document.filename,\n                file_type: file.type,\n                file_size: file.size,\n                status: result.document.status,\n                chunks_count: result.document.chunks_processed || 0,\n                created_at: new Date().toISOString()\n            };\n            console.log(\"[DocumentUpload] Adding optimistic document:\", optimisticDocument);\n            setDocuments((prev)=>[\n                    optimisticDocument,\n                    ...prev\n                ]);\n            // Small delay to allow database transaction to commit, then reload with retry logic\n            setTimeout(async ()=>{\n                console.log(\"[DocumentUpload] Starting document list refresh for document: \".concat(result.document.id));\n                await loadDocuments(0, result.document.id);\n            }, 200);\n            // Call callback if provided\n            onDocumentUploaded === null || onDocumentUploaded === void 0 ? void 0 : onDocumentUploaded();\n        } catch (err) {\n            const errorMessage = \"Upload failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        } finally{\n            setIsUploading(false);\n            setUploadProgress(0);\n            // Clear file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Auto-clear success message after 5 seconds\n            if (success) {\n                setTimeout(()=>setSuccess(null), 5000);\n            }\n        }\n    };\n    // Handle drag and drop\n    const handleDrag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrag]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.type === 'dragenter' || e.type === 'dragover') {\n                setDragActive(true);\n            } else if (e.type === 'dragleave') {\n                setDragActive(false);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrag]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragActive(false);\n            if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n                handleFileUpload(e.dataTransfer.files);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrop]\"], [\n        configId\n    ]);\n    // Handle file input change\n    const handleInputChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            handleFileUpload(e.target.files);\n        }\n    };\n    // Delete document\n    const handleDeleteDocument = async (documentId)=>{\n        if (!confirm('Are you sure you want to delete this document?')) return;\n        // Optimistically remove the document from the list\n        const originalDocuments = documents;\n        setDocuments((prev)=>prev.filter((doc)=>doc.id !== documentId));\n        try {\n            const response = await fetch(\"/api/documents/\".concat(documentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                // Restore the original list on error\n                setDocuments(originalDocuments);\n                throw new Error('Failed to delete document');\n            }\n            setSuccess('Document deleted successfully');\n            // Refresh the list to ensure consistency\n            await loadDocuments();\n            // Auto-clear success message after 3 seconds\n            setTimeout(()=>setSuccess(null), 3000);\n        } catch (err) {\n            // Restore the original list on error\n            setDocuments(originalDocuments);\n            const errorMessage = \"Delete failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        }\n    };\n    // Format file size\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // Get file icon\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes('pdf')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 273,\n            columnNumber: 42\n        }, this);\n        if (fileType.includes('word')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 274,\n            columnNumber: 43\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-5 h-5 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 275,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm font-medium\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-800 text-sm font-medium\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform \".concat(dragActive ? 'border-orange-400 bg-orange-50 scale-105 shadow-lg' : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md', \" \").concat(!configId ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                onDragEnter: handleDrag,\n                onDragLeave: handleDrag,\n                onDragOver: handleDrag,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return configId && ((_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click());\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        accept: \".pdf,.txt,.md\",\n                        onChange: handleInputChange,\n                        disabled: !configId || isUploading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, this),\n                    isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-12 h-12 text-orange-500 mx-auto animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Processing Document...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(uploadProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            uploadProgress,\n                                            \"% complete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: configId ? 'Upload Knowledge Documents' : 'Select a configuration first'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: \"Drag and drop files here, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: \"Supports PDF, TXT, MD files up to 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Uploaded Documents\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            getFileIcon(doc.file_type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: doc.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            formatFileSize(doc.file_size),\n                                                            \" • \",\n                                                            doc.chunks_count,\n                                                            \" chunks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    doc.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-orange-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(doc.status === 'completed' ? 'text-green-600' : doc.status === 'processing' ? 'text-orange-600' : 'text-red-600'),\n                                                        children: doc.status === 'completed' ? 'Ready' : doc.status === 'processing' ? 'Processing' : 'Failed'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteDocument(doc.id),\n                                                className: \"p-1 text-gray-400 hover:text-red-500 transition-colors\",\n                                                title: \"Delete document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, doc.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"Wg3Csz6qp0hbrL5TIpe/0qAcs6A=\");\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentUpload.tsx\n"));

/***/ })

});