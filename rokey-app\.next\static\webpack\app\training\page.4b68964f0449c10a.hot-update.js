"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/DocumentUpload.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentUpload.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DocumentUpload(param) {\n    let { configId, onDocumentUploaded } = param;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Load documents for the current config with retry logic\n    const loadDocuments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[loadDocuments]\": async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, expectedDocumentId = arguments.length > 1 ? arguments[1] : void 0;\n            if (!configId) return;\n            try {\n                const response = await fetch(\"/api/documents/list?configId=\".concat(configId));\n                if (response.ok) {\n                    const data = await response.json();\n                    const newDocuments = data.documents || [];\n                    // If we're looking for a specific document and it's not found, retry\n                    if (expectedDocumentId && retryCount < 3) {\n                        const foundDocument = newDocuments.find({\n                            \"DocumentUpload.useCallback[loadDocuments].foundDocument\": (doc)=>doc.id === expectedDocumentId\n                        }[\"DocumentUpload.useCallback[loadDocuments].foundDocument\"]);\n                        if (!foundDocument) {\n                            console.log(\"[DocumentUpload] Document \".concat(expectedDocumentId, \" not found, retrying in \").concat((retryCount + 1) * 500, \"ms...\"));\n                            setTimeout({\n                                \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                                    loadDocuments(retryCount + 1, expectedDocumentId);\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"], (retryCount + 1) * 500); // 500ms, 1s, 1.5s delays\n                            return;\n                        }\n                    }\n                    setDocuments(newDocuments);\n                }\n            } catch (err) {\n                console.error('Failed to load documents:', err);\n                // Retry on error if we haven't exceeded retry count\n                if (retryCount < 2) {\n                    setTimeout({\n                        \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                            loadDocuments(retryCount + 1, expectedDocumentId);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"], 1000);\n                }\n            }\n        }\n    }[\"DocumentUpload.useCallback[loadDocuments]\"], [\n        configId\n    ]);\n    // Load documents when configId changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"DocumentUpload.useEffect\": ()=>{\n            loadDocuments();\n        }\n    }[\"DocumentUpload.useEffect\"], [\n        loadDocuments\n    ]);\n    // Handle file upload\n    const handleFileUpload = async (files)=>{\n        if (!configId) {\n            setError('Please select an API configuration first');\n            return;\n        }\n        const file = files[0];\n        if (!file) return;\n        // Validate file type\n        const allowedTypes = [\n            'application/pdf',\n            'text/plain',\n            'text/markdown'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setError('Please upload PDF, TXT, or MD files only');\n            return;\n        }\n        // Validate file size (10MB max)\n        if (file.size > 10 * 1024 * 1024) {\n            setError('File size must be less than 10MB');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(null);\n        setUploadProgress(0);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('configId', configId);\n            // Simulate progress for better UX\n            const progressInterval = setInterval(()=>{\n                setUploadProgress((prev)=>Math.min(prev + 10, 90));\n            }, 200);\n            const response = await fetch('/api/documents/upload', {\n                method: 'POST',\n                body: formData\n            });\n            clearInterval(progressInterval);\n            setUploadProgress(100);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Upload failed');\n            }\n            const result = await response.json();\n            setSuccess(\"✨ \".concat(file.name, \" uploaded successfully! Processing \").concat(result.document.chunks_total, \" chunks.\"));\n            // Optimistically add the document to the list immediately\n            const optimisticDocument = {\n                id: result.document.id,\n                filename: result.document.filename,\n                file_type: file.type,\n                file_size: file.size,\n                status: result.document.status,\n                chunks_count: result.document.chunks_processed || 0,\n                created_at: new Date().toISOString()\n            };\n            setDocuments((prev)=>[\n                    optimisticDocument,\n                    ...prev\n                ]);\n            // Reload documents list with retry logic to ensure the new document appears\n            await loadDocuments(0, result.document.id);\n            // Call callback if provided\n            onDocumentUploaded === null || onDocumentUploaded === void 0 ? void 0 : onDocumentUploaded();\n        } catch (err) {\n            const errorMessage = \"Upload failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        } finally{\n            setIsUploading(false);\n            setUploadProgress(0);\n            // Clear file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Auto-clear success message after 5 seconds\n            if (success) {\n                setTimeout(()=>setSuccess(null), 5000);\n            }\n        }\n    };\n    // Handle drag and drop\n    const handleDrag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrag]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.type === 'dragenter' || e.type === 'dragover') {\n                setDragActive(true);\n            } else if (e.type === 'dragleave') {\n                setDragActive(false);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrag]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragActive(false);\n            if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n                handleFileUpload(e.dataTransfer.files);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrop]\"], [\n        configId\n    ]);\n    // Handle file input change\n    const handleInputChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            handleFileUpload(e.target.files);\n        }\n    };\n    // Delete document\n    const handleDeleteDocument = async (documentId)=>{\n        if (!confirm('Are you sure you want to delete this document?')) return;\n        try {\n            const response = await fetch(\"/api/documents/\".concat(documentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                throw new Error('Failed to delete document');\n            }\n            setSuccess('Document deleted successfully');\n            await loadDocuments();\n            // Auto-clear success message after 3 seconds\n            setTimeout(()=>setSuccess(null), 3000);\n        } catch (err) {\n            const errorMessage = \"Delete failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        }\n    };\n    // Format file size\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // Get file icon\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes('pdf')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 237,\n            columnNumber: 42\n        }, this);\n        if (fileType.includes('word')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 238,\n            columnNumber: 43\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-5 h-5 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 239,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm font-medium\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-800 text-sm font-medium\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform \".concat(dragActive ? 'border-orange-400 bg-orange-50 scale-105 shadow-lg' : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md', \" \").concat(!configId ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                onDragEnter: handleDrag,\n                onDragLeave: handleDrag,\n                onDragOver: handleDrag,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return configId && ((_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click());\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        accept: \".pdf,.txt,.md\",\n                        onChange: handleInputChange,\n                        disabled: !configId || isUploading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this),\n                    isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-12 h-12 text-orange-500 mx-auto animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Processing Document...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(uploadProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            uploadProgress,\n                                            \"% complete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: configId ? 'Upload Knowledge Documents' : 'Select a configuration first'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: \"Drag and drop files here, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: \"Supports PDF, TXT, MD files up to 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this),\n            documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Uploaded Documents\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            getFileIcon(doc.file_type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: doc.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            formatFileSize(doc.file_size),\n                                                            \" • \",\n                                                            doc.chunks_count,\n                                                            \" chunks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    doc.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-orange-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(doc.status === 'completed' ? 'text-green-600' : doc.status === 'processing' ? 'text-orange-600' : 'text-red-600'),\n                                                        children: doc.status === 'completed' ? 'Ready' : doc.status === 'processing' ? 'Processing' : 'Failed'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteDocument(doc.id),\n                                                className: \"p-1 text-gray-400 hover:text-red-500 transition-colors\",\n                                                title: \"Delete document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, doc.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 319,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"Xcn8e1tJoWV2UOsD5YWkWjLmy20=\");\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentUpload.tsx\n"));

/***/ })

});