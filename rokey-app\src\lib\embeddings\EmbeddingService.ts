/**
 * Enhanced Embedding Service with Multiple Providers
 * Supports better embedding models than Gemini embedding-001
 */

import { GoogleGenerativeAIEmbeddings } from '@langchain/google-genai';

export interface EmbeddingProvider {
  name: string;
  dimensions: number;
  maxTokens: number;
  description: string;
  embedQuery(text: string): Promise<number[]>;
}

export interface EmbeddingConfig {
  provider: 'gemini' | 'huggingface' | 'cohere' | 'openai';
  model: string;
  apiKey?: string;
  endpoint?: string;
}

/**
 * Gemini Embedding Provider (current)
 */
class GeminiEmbeddingProvider implements EmbeddingProvider {
  name = 'Gemini embedding-001';
  dimensions = 768;
  maxTokens = 3072;
  description = 'Google Gemini embedding model - has content filtering issues';
  
  private embeddings: GoogleGenerativeAIEmbeddings;

  constructor(apiKey: string) {
    this.embeddings = new GoogleGenerativeAIEmbeddings({
      apiKey,
      modelName: "embedding-001",
    });
  }

  async embedQuery(text: string): Promise<number[]> {
    return await this.embeddings.embedQuery(text);
  }
}

/**
 * Hugging Face Embedding Provider (Free & Better)
 */
class HuggingFaceEmbeddingProvider implements EmbeddingProvider {
  name: string;
  dimensions: number;
  maxTokens: number;
  description: string;
  
  private apiKey?: string;
  private model: string;

  constructor(model: string, apiKey?: string) {
    this.model = model;
    this.apiKey = apiKey;
    
    // Model-specific configurations
    switch (model) {
      case 'BAAI/bge-large-en-v1.5':
        this.name = 'BGE Large EN v1.5';
        this.dimensions = 1024;
        this.maxTokens = 512;
        this.description = 'Top-performing free embedding model, excellent for English';
        break;
      case 'sentence-transformers/all-MiniLM-L6-v2':
        this.name = 'MiniLM L6 v2';
        this.dimensions = 384;
        this.maxTokens = 256;
        this.description = 'Fast and efficient, good for most use cases';
        break;
      case 'Snowflake/snowflake-arctic-embed-l':
        this.name = 'Snowflake Arctic Embed Large';
        this.dimensions = 1024;
        this.maxTokens = 512;
        this.description = 'Best overall performance, optimized for retrieval';
        break;
      default:
        this.name = model;
        this.dimensions = 768;
        this.maxTokens = 512;
        this.description = 'Custom Hugging Face model';
    }
  }

  async embedQuery(text: string): Promise<number[]> {
    const endpoint = this.apiKey 
      ? `https://api-inference.huggingface.co/pipeline/feature-extraction/${this.model}`
      : `https://api-inference.huggingface.co/models/${this.model}`;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }

    const response = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        inputs: text,
        options: { wait_for_model: true }
      }),
    });

    if (!response.ok) {
      throw new Error(`Hugging Face API error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    
    // Handle different response formats
    if (Array.isArray(result) && Array.isArray(result[0])) {
      return result[0]; // Standard format
    } else if (Array.isArray(result)) {
      return result; // Direct array
    } else {
      throw new Error('Unexpected response format from Hugging Face API');
    }
  }
}

/**
 * OpenAI Embedding Provider (Paid but excellent)
 */
class OpenAIEmbeddingProvider implements EmbeddingProvider {
  name: string;
  dimensions: number;
  maxTokens: number;
  description: string;
  
  private apiKey: string;
  private model: string;

  constructor(model: string, apiKey: string) {
    this.model = model;
    this.apiKey = apiKey;
    
    switch (model) {
      case 'text-embedding-3-small':
        this.name = 'OpenAI Embedding 3 Small';
        this.dimensions = 1536;
        this.maxTokens = 8191;
        this.description = 'OpenAI latest small embedding model';
        break;
      case 'text-embedding-3-large':
        this.name = 'OpenAI Embedding 3 Large';
        this.dimensions = 3072;
        this.maxTokens = 8191;
        this.description = 'OpenAI latest large embedding model';
        break;
      default:
        this.name = model;
        this.dimensions = 1536;
        this.maxTokens = 8191;
        this.description = 'OpenAI embedding model';
    }
  }

  async embedQuery(text: string): Promise<number[]> {
    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify({
        model: this.model,
        input: text,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    return result.data[0].embedding;
  }
}

/**
 * Main Embedding Service
 */
export class EmbeddingService {
  private provider: EmbeddingProvider;

  constructor(config: EmbeddingConfig) {
    switch (config.provider) {
      case 'gemini':
        if (!config.apiKey) throw new Error('Gemini API key required');
        this.provider = new GeminiEmbeddingProvider(config.apiKey);
        break;
      
      case 'huggingface':
        this.provider = new HuggingFaceEmbeddingProvider(config.model, config.apiKey);
        break;
      
      case 'openai':
        if (!config.apiKey) throw new Error('OpenAI API key required');
        this.provider = new OpenAIEmbeddingProvider(config.model, config.apiKey);
        break;
      
      default:
        throw new Error(`Unsupported embedding provider: ${config.provider}`);
    }
  }

  async embedQuery(text: string): Promise<number[]> {
    return await this.provider.embedQuery(text);
  }

  getProviderInfo() {
    return {
      name: this.provider.name,
      dimensions: this.provider.dimensions,
      maxTokens: this.provider.maxTokens,
      description: this.provider.description,
    };
  }
}

/**
 * Factory function to create embedding service based on environment
 */
export function createEmbeddingService(): EmbeddingService {
  // Check for available API keys and choose the best option
  const geminiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;
  const openaiKey = process.env.OPENAI_API_KEY;
  const hfKey = process.env.HUGGINGFACE_API_KEY;

  // Priority order: OpenAI > Hugging Face > Gemini
  if (openaiKey) {
    return new EmbeddingService({
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: openaiKey,
    });
  }

  if (hfKey) {
    return new EmbeddingService({
      provider: 'huggingface',
      model: 'BAAI/bge-large-en-v1.5', // Best free option
      apiKey: hfKey,
    });
  }

  // Fallback to free Hugging Face (no API key needed, but rate limited)
  return new EmbeddingService({
    provider: 'huggingface',
    model: 'sentence-transformers/all-MiniLM-L6-v2', // Smaller, more reliable for free tier
  });

  // Last resort: Gemini (with content filtering issues)
  if (geminiKey) {
    return new EmbeddingService({
      provider: 'gemini',
      model: 'embedding-001',
      apiKey: geminiKey,
    });
  }

  throw new Error('No embedding API keys available. Please set OPENAI_API_KEY, HUGGINGFACE_API_KEY, or ROKEY_CLASSIFICATION_GEMINI_API_KEY');
}
