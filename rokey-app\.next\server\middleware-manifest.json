{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "L13E5qMAnjanbv_JCl6ug", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "MQ6n+AXoa6Rux06XbSq+6wJ20O0xvuFjerK9xZRPHR4=", "__NEXT_PREVIEW_MODE_ID": "1ee8f9e6a3c1ac0c391ef6794a2cc85d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4f5d6afb9d39ecdf5b781844ca67d0c7d71a48754fc279d503887d2f25fc5efd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ce6ad2427a0fa2789e8089952512542174c3b31938dff61fd221187bb72c16ab"}}}, "functions": {}, "sortedMiddleware": ["/"]}