"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/DocumentUpload.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentUpload.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DocumentUpload(param) {\n    let { configId, onDocumentUploaded } = param;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Load documents for the current config with retry logic\n    const loadDocuments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[loadDocuments]\": async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, expectedDocumentId = arguments.length > 1 ? arguments[1] : void 0;\n            if (!configId) return;\n            try {\n                const response = await fetch(\"/api/documents/list?configId=\".concat(configId));\n                if (response.ok) {\n                    const data = await response.json();\n                    const newDocuments = data.documents || [];\n                    // If we're looking for a specific document and it's not found, retry\n                    if (expectedDocumentId && retryCount < 3) {\n                        const foundDocument = newDocuments.find({\n                            \"DocumentUpload.useCallback[loadDocuments].foundDocument\": (doc)=>doc.id === expectedDocumentId\n                        }[\"DocumentUpload.useCallback[loadDocuments].foundDocument\"]);\n                        if (!foundDocument) {\n                            console.log(\"[DocumentUpload] Document \".concat(expectedDocumentId, \" not found, retrying in \").concat((retryCount + 1) * 500, \"ms...\"));\n                            setTimeout({\n                                \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                                    loadDocuments(retryCount + 1, expectedDocumentId);\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"], (retryCount + 1) * 500); // 500ms, 1s, 1.5s delays\n                            return;\n                        }\n                    }\n                    setDocuments(newDocuments);\n                }\n            } catch (err) {\n                console.error('Failed to load documents:', err);\n                // Retry on error if we haven't exceeded retry count\n                if (retryCount < 2) {\n                    setTimeout({\n                        \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                            loadDocuments(retryCount + 1, expectedDocumentId);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"], 1000);\n                }\n            }\n        }\n    }[\"DocumentUpload.useCallback[loadDocuments]\"], [\n        configId\n    ]);\n    // Load documents when configId changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"DocumentUpload.useEffect\": ()=>{\n            loadDocuments();\n        }\n    }[\"DocumentUpload.useEffect\"], [\n        loadDocuments\n    ]);\n    // Handle file upload\n    const handleFileUpload = async (files)=>{\n        if (!configId) {\n            setError('Please select an API configuration first');\n            return;\n        }\n        const file = files[0];\n        if (!file) return;\n        // Validate file type\n        const allowedTypes = [\n            'application/pdf',\n            'text/plain',\n            'text/markdown'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setError('Please upload PDF, TXT, or MD files only');\n            return;\n        }\n        // Validate file size (10MB max)\n        if (file.size > 10 * 1024 * 1024) {\n            setError('File size must be less than 10MB');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(null);\n        setUploadProgress(0);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('configId', configId);\n            // Simulate progress for better UX\n            const progressInterval = setInterval(()=>{\n                setUploadProgress((prev)=>Math.min(prev + 10, 90));\n            }, 200);\n            const response = await fetch('/api/documents/upload', {\n                method: 'POST',\n                body: formData\n            });\n            clearInterval(progressInterval);\n            setUploadProgress(100);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Upload failed');\n            }\n            const result = await response.json();\n            setSuccess(\"✨ \".concat(file.name, \" uploaded successfully! Processing \").concat(result.document.chunks_total, \" chunks.\"));\n            // Reload documents list\n            await loadDocuments();\n            // Call callback if provided\n            onDocumentUploaded === null || onDocumentUploaded === void 0 ? void 0 : onDocumentUploaded();\n        } catch (err) {\n            const errorMessage = \"Upload failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        } finally{\n            setIsUploading(false);\n            setUploadProgress(0);\n            // Clear file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Auto-clear success message after 5 seconds\n            if (success) {\n                setTimeout(()=>setSuccess(null), 5000);\n            }\n        }\n    };\n    // Handle drag and drop\n    const handleDrag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrag]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.type === 'dragenter' || e.type === 'dragover') {\n                setDragActive(true);\n            } else if (e.type === 'dragleave') {\n                setDragActive(false);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrag]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragActive(false);\n            if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n                handleFileUpload(e.dataTransfer.files);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrop]\"], [\n        configId\n    ]);\n    // Handle file input change\n    const handleInputChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            handleFileUpload(e.target.files);\n        }\n    };\n    // Delete document\n    const handleDeleteDocument = async (documentId)=>{\n        if (!confirm('Are you sure you want to delete this document?')) return;\n        try {\n            const response = await fetch(\"/api/documents/\".concat(documentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) {\n                throw new Error('Failed to delete document');\n            }\n            setSuccess('Document deleted successfully');\n            await loadDocuments();\n            // Auto-clear success message after 3 seconds\n            setTimeout(()=>setSuccess(null), 3000);\n        } catch (err) {\n            const errorMessage = \"Delete failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        }\n    };\n    // Format file size\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // Get file icon\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes('pdf')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 224,\n            columnNumber: 42\n        }, this);\n        if (fileType.includes('word')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 225,\n            columnNumber: 43\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-5 h-5 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 226,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-5 h-5 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm font-medium\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-800 text-sm font-medium\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform \".concat(dragActive ? 'border-orange-400 bg-orange-50 scale-105 shadow-lg' : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md', \" \").concat(!configId ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                onDragEnter: handleDrag,\n                onDragLeave: handleDrag,\n                onDragOver: handleDrag,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return configId && ((_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click());\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        accept: \".pdf,.txt,.md\",\n                        onChange: handleInputChange,\n                        disabled: !configId || isUploading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this),\n                    isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-12 h-12 text-orange-500 mx-auto animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Processing Document...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(uploadProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            uploadProgress,\n                                            \"% complete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: configId ? 'Upload Knowledge Documents' : 'Select a configuration first'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: \"Drag and drop files here, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: \"Supports PDF, TXT, MD files up to 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Uploaded Documents\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            getFileIcon(doc.file_type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: doc.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            formatFileSize(doc.file_size),\n                                                            \" • \",\n                                                            doc.chunks_count,\n                                                            \" chunks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    doc.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-orange-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(doc.status === 'completed' ? 'text-green-600' : doc.status === 'processing' ? 'text-orange-600' : 'text-red-600'),\n                                                        children: doc.status === 'completed' ? 'Ready' : doc.status === 'processing' ? 'Processing' : 'Failed'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteDocument(doc.id),\n                                                className: \"p-1 text-gray-400 hover:text-red-500 transition-colors\",\n                                                title: \"Delete document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, doc.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n        lineNumber: 230,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"Xcn8e1tJoWV2UOsD5YWkWjLmy20=\");\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentUpload.tsx\n"));

/***/ })

});