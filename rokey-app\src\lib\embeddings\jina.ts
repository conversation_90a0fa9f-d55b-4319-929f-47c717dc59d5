/**
 * Multi-Key Jina Embeddings v3 Implementation
 * Provides automatic key rotation and high rate limits for RouKey
 */

interface JinaEmbeddingResponse {
  data: Array<{
    object: string;
    index: number;
    embedding: number[];
  }>;
  model: string;
  usage: {
    total_tokens: number;
    prompt_tokens: number;
  };
}

interface KeyUsageStats {
  requests: number;
  tokens: number;
  lastUsed: Date;
  errors: number;
  lastError?: Date;
}

export class MultiKeyJinaEmbeddings {
  private apiKeys: string[];
  private currentKeyIndex = 0;
  private keyUsage = new Map<string, KeyUsageStats>();
  private baseUrl = 'https://api.jina.ai/v1/embeddings';
  private model = 'jina-embeddings-v3';

  constructor() {
    // Load all Jina API keys from environment
    this.apiKeys = [
      process.env.JINA_API_KEY,
      process.env.JINA_API_KEY_2,
      process.env.JINA_API_KEY_3,
      process.env.JINA_API_KEY_4,
      process.env.JINA_API_KEY_5,
      process.env.JINA_API_KEY_6,
      process.env.JINA_API_KEY_7,
      process.env.JINA_API_KEY_9,
      process.env.JINA_API_KEY_10,
    ].filter(Boolean) as string[];

    if (this.apiKeys.length === 0) {
      throw new Error('No Jina API keys found in environment variables');
    }

    console.log(`[Jina Embeddings] Initialized with ${this.apiKeys.length} API keys`);

    // Initialize usage stats for each key
    this.apiKeys.forEach(key => {
      this.keyUsage.set(key, {
        requests: 0,
        tokens: 0,
        lastUsed: new Date(),
        errors: 0
      });
    });
  }

  /**
   * Get the next API key using round-robin rotation
   */
  private getNextKey(): string {
    const key = this.apiKeys[this.currentKeyIndex];
    this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
    return key;
  }

  /**
   * Get the best available API key based on usage and error rates
   */
  private getBestKey(): string {
    // For now, use simple round-robin
    // TODO: Implement smart selection based on usage stats
    return this.getNextKey();
  }

  /**
   * Update usage statistics for a key
   */
  private updateKeyUsage(apiKey: string, tokens: number, isError = false) {
    const stats = this.keyUsage.get(apiKey);
    if (stats) {
      stats.requests++;
      stats.tokens += tokens;
      stats.lastUsed = new Date();
      
      if (isError) {
        stats.errors++;
        stats.lastError = new Date();
      }
    }
  }

  /**
   * Generate embedding for a single text input
   */
  async embedQuery(text: string): Promise<number[]> {
    const maxRetries = this.apiKeys.length;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const apiKey = this.getBestKey();
        
        console.log(`[Jina Embeddings] Attempt ${attempt + 1}/${maxRetries} with key ${this.apiKeys.indexOf(apiKey) + 1}`);

        const response = await fetch(this.baseUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: this.model,
            input: [text],
            normalized: true,
            embedding_type: 'float'
          }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          
          if (response.status === 429) {
            console.log(`[Jina Embeddings] Rate limit hit for key ${this.apiKeys.indexOf(apiKey) + 1}, trying next key...`);
            this.updateKeyUsage(apiKey, 0, true);
            continue;
          }

          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data: JinaEmbeddingResponse = await response.json();
        
        if (!data.data || data.data.length === 0) {
          throw new Error('No embedding data returned from Jina API');
        }

        const embedding = data.data[0].embedding;
        
        // Update usage stats
        this.updateKeyUsage(apiKey, data.usage?.total_tokens || text.length);
        
        console.log(`[Jina Embeddings] Success with key ${this.apiKeys.indexOf(apiKey) + 1} (${embedding.length} dimensions, ${data.usage?.total_tokens || 'unknown'} tokens)`);
        
        return embedding;

      } catch (error: any) {
        lastError = error as Error;
        console.log(`[Jina Embeddings] Attempt ${attempt + 1} failed:`, error.message);
        
        // If this is the last attempt, throw the error
        if (attempt === maxRetries - 1) {
          break;
        }
      }
    }

    // All keys failed
    console.error(`[Jina Embeddings] All ${maxRetries} API keys failed`);
    throw new Error(`All Jina API keys failed. Last error: ${lastError?.message || 'Unknown error'}`);
  }

  /**
   * Generate embeddings for multiple texts (batch processing)
   */
  async embedDocuments(texts: string[]): Promise<number[][]> {
    // For now, process sequentially to avoid overwhelming the API
    // TODO: Implement smart batching based on rate limits
    const embeddings: number[][] = [];
    
    for (let i = 0; i < texts.length; i++) {
      console.log(`[Jina Embeddings] Processing document ${i + 1}/${texts.length}`);
      const embedding = await this.embedQuery(texts[i]);
      embeddings.push(embedding);
      
      // Small delay to respect rate limits
      if (i < texts.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return embeddings;
  }

  /**
   * Get usage statistics for all keys
   */
  getUsageStats(): Record<string, KeyUsageStats> {
    const stats: Record<string, KeyUsageStats> = {};
    
    this.apiKeys.forEach((key, index) => {
      const keyStats = this.keyUsage.get(key);
      if (keyStats) {
        stats[`key_${index + 1}`] = { ...keyStats };
      }
    });
    
    return stats;
  }

  /**
   * Get total capacity across all keys
   */
  getTotalCapacity(): {
    totalKeys: number;
    estimatedRPM: number;
    estimatedTokensPerMonth: number;
  } {
    return {
      totalKeys: this.apiKeys.length,
      estimatedRPM: this.apiKeys.length * 500, // 500 RPM per key
      estimatedTokensPerMonth: this.apiKeys.length * 1000000, // 1M tokens per key
    };
  }
}

// Export a singleton instance
export const jinaEmbeddings = new MultiKeyJinaEmbeddings();
