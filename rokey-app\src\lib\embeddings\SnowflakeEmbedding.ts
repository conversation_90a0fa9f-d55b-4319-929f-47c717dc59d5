/**
 * Snowflake Arctic Embed Large - Commercial-friendly embedding service
 * Replaces Google Gemini embedding-001 to eliminate content censorship
 * 
 * Features:
 * - 1024 dimensions (vs Gemini's 768)
 * - No content filtering/censorship
 * - Apache 2.0 license (commercial use allowed)
 * - Better performance on MTEB benchmarks
 */

export interface EmbeddingResult {
  embedding: number[];
  dimensions: number;
  model: string;
}

export class SnowflakeEmbedding {
  private readonly modelName = 'Snowflake/snowflake-arctic-embed-l';
  private readonly dimensions = 1024;
  private readonly maxTokens = 512;
  private readonly apiKey?: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey;
    console.log(`[SnowflakeEmbedding] Initialized with model: ${this.modelName}`);
  }

  /**
   * Generate embedding for a single text query
   */
  async embedQuery(text: string): Promise<number[]> {
    try {
      console.log(`[SnowflakeEmbedding] Generating embedding for text: ${text.substring(0, 100)}...`);
      
      const result = await this.callHuggingFaceAPI(text);
      
      if (!Array.isArray(result) || result.length !== this.dimensions) {
        throw new Error(`Invalid embedding dimensions: expected ${this.dimensions}, got ${result?.length || 'undefined'}`);
      }

      console.log(`[SnowflakeEmbedding] Successfully generated ${this.dimensions}D embedding`);
      return result;
    } catch (error) {
      console.error('[SnowflakeEmbedding] Error generating embedding:', error);
      throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate embeddings for multiple texts (batch processing)
   */
  async embedDocuments(texts: string[]): Promise<number[][]> {
    console.log(`[SnowflakeEmbedding] Generating embeddings for ${texts.length} documents`);
    
    // Process in batches to avoid API limits
    const batchSize = 10;
    const results: number[][] = [];
    
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      console.log(`[SnowflakeEmbedding] Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(texts.length / batchSize)}`);
      
      const batchPromises = batch.map(text => this.embedQuery(text));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Small delay between batches to be respectful to the API
      if (i + batchSize < texts.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    console.log(`[SnowflakeEmbedding] Successfully generated ${results.length} embeddings`);
    return results;
  }

  /**
   * Call Hugging Face Inference API
   */
  private async callHuggingFaceAPI(text: string): Promise<number[]> {
    const endpoint = `https://api-inference.huggingface.co/models/${this.modelName}`;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add API key if available (for higher rate limits)
    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }

    const requestBody = {
      inputs: text,
      options: {
        wait_for_model: true,
        use_cache: false, // Ensure fresh results
      }
    };

    console.log(`[SnowflakeEmbedding] Calling Hugging Face API: ${endpoint}`);

    const response = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[SnowflakeEmbedding] API Error: ${response.status} ${response.statusText}`, errorText);
      
      if (response.status === 503) {
        throw new Error('Model is loading, please try again in a few moments');
      } else if (response.status === 429) {
        throw new Error('Rate limit exceeded. Consider adding HUGGINGFACE_API_KEY to environment variables for higher limits');
      } else {
        throw new Error(`Hugging Face API error: ${response.status} ${response.statusText}`);
      }
    }

    const result = await response.json();
    
    // Handle different response formats from Hugging Face
    if (Array.isArray(result)) {
      // Direct array response
      return result;
    } else if (result && Array.isArray(result[0])) {
      // Nested array response
      return result[0];
    } else if (result && result.embeddings && Array.isArray(result.embeddings[0])) {
      // Structured response
      return result.embeddings[0];
    } else {
      console.error('[SnowflakeEmbedding] Unexpected API response format:', result);
      throw new Error('Unexpected response format from Hugging Face API');
    }
  }

  /**
   * Get model information
   */
  getModelInfo() {
    return {
      name: 'Snowflake Arctic Embed Large',
      model: this.modelName,
      dimensions: this.dimensions,
      maxTokens: this.maxTokens,
      license: 'Apache 2.0',
      description: 'Commercial-friendly embedding model with no content filtering',
      features: [
        'No content censorship',
        'Commercial use allowed',
        'Better performance than Gemini',
        'Higher dimensional embeddings (1024D)',
      ]
    };
  }

  /**
   * Check if the service is properly configured
   */
  async healthCheck(): Promise<boolean> {
    try {
      console.log('[SnowflakeEmbedding] Performing health check...');
      const testEmbedding = await this.embedQuery('test');
      const isHealthy = Array.isArray(testEmbedding) && testEmbedding.length === this.dimensions;
      console.log(`[SnowflakeEmbedding] Health check ${isHealthy ? 'passed' : 'failed'}`);
      return isHealthy;
    } catch (error) {
      console.error('[SnowflakeEmbedding] Health check failed:', error);
      return false;
    }
  }
}

/**
 * Factory function to create Snowflake embedding service
 */
export function createSnowflakeEmbedding(): SnowflakeEmbedding {
  const apiKey = process.env.HUGGINGFACE_API_KEY;
  
  if (!apiKey) {
    console.warn('[SnowflakeEmbedding] No HUGGINGFACE_API_KEY found. Using free tier with rate limits.');
    console.warn('[SnowflakeEmbedding] For production use, consider adding HUGGINGFACE_API_KEY to your environment variables.');
  }
  
  return new SnowflakeEmbedding(apiKey);
}

/**
 * Legacy compatibility wrapper for Gemini-style API
 */
export class SnowflakeEmbeddingLegacy {
  private snowflake: SnowflakeEmbedding;

  constructor(config?: { apiKey?: string }) {
    this.snowflake = new SnowflakeEmbedding(config?.apiKey);
  }

  async embedQuery(text: string): Promise<number[]> {
    return await this.snowflake.embedQuery(text);
  }

  async embedDocuments(texts: string[]): Promise<number[][]> {
    return await this.snowflake.embedDocuments(texts);
  }
}
