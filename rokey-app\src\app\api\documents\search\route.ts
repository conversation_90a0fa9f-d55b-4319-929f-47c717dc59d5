import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { createSnowflakeEmbedding } from '@/lib/embeddings/SnowflakeEmbedding';

// Initialize embeddings with Snowflake Arctic Embed Large
// No content filtering, commercial-friendly, better performance than Gemini
const embeddings = createSnowflakeEmbedding();

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { query, configId, limit = 5, threshold = 0.7 } = body;

    if (!query || !configId) {
      return NextResponse.json({ 
        error: 'Query and configId are required' 
      }, { status: 400 });
    }

    console.log(`[Document Search] Searching for: "${query}" in config: ${configId}`);

    // Generate embedding for the search query using Snowflake Arctic
    // This will find contact details and personal information without censoring
    console.log(`[Document Search] Generating Snowflake embedding for query`);
    const queryEmbedding = await embeddings.embedQuery(query);
    console.log(`[Document Search] Generated ${queryEmbedding.length}D embedding for search query`);
    
    // Search for similar document chunks using our custom function
    const { data: results, error } = await supabase.rpc('search_document_chunks', {
      query_embedding: queryEmbedding,
      config_id: configId,
      user_id_param: user.id,
      match_threshold: threshold,
      match_count: limit
    });

    if (error) {
      console.error('[Document Search] Search error:', error);
      return NextResponse.json({
        error: 'Search failed',
        details: error.message
      }, { status: 500 });
    }

    console.log(`[Document Search] Found ${results?.length || 0} relevant chunks`);

    // Get document metadata for the results
    if (results && results.length > 0) {
      const documentIds = [...new Set(results.map((r: any) => r.document_id))];
      
      const { data: documents } = await supabase
        .from('documents')
        .select('id, filename, file_type')
        .in('id', documentIds);

      // Enhance results with document metadata
      const enhancedResults = results.map((result: any) => {
        const document = documents?.find(d => d.id === result.document_id);
        return {
          ...result,
          document: document || null
        };
      });

      return NextResponse.json({
        success: true,
        results: enhancedResults,
        query,
        total_results: results.length
      });
    }

    return NextResponse.json({
      success: true,
      results: [],
      query,
      total_results: 0
    });

  } catch (error: any) {
    console.error('[Document Search] Error:', error);
    return NextResponse.json({
      error: 'Search failed',
      details: error.message
    }, { status: 500 });
  }
}
